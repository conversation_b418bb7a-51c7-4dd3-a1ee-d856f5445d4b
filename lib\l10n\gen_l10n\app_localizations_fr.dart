// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for French (`fr`).
class AppLocalizationsFr extends AppLocalizations {
  AppLocalizationsFr([String locale = 'fr']) : super(locale);

  @override
  String get language => 'Langue';

  @override
  String get dashboard => 'Tableau de bord';

  @override
  String get reception => 'Réception';

  @override
  String get clients => 'Clients';

  @override
  String get devices => 'Appareils';

  @override
  String get device => 'Appareil';

  @override
  String get details => 'Détails';

  @override
  String get receiptList => 'Liste des reçus';

  @override
  String get cashFlow => 'Flux de trésorerie';

  @override
  String get expenses => 'Dépenses';

  @override
  String get products => 'Produits';

  @override
  String get technicians => 'Techniciens';

  @override
  String get settings => 'Paramètres';

  @override
  String get exitApp => 'Confirmer la fermeture';

  @override
  String get exitAppMsg => 'Etes-vous sûr de vouloir fermer cette fenêtre ?';

  @override
  String get yes => 'Oui';

  @override
  String get no => 'Non';

  @override
  String get new_add => 'Nouveau';

  @override
  String get add => 'Ajouter';

  @override
  String get edit => 'Modifier';

  @override
  String get cancel => 'Annuler';

  @override
  String get delete => 'Supprimer';

  @override
  String get select => 'Sélectionner';

  @override
  String get name => 'Nom';

  @override
  String get type => 'Type';

  @override
  String get phone_n => 'N° Télé';

  @override
  String get device_type => 'Type d\'appareil';

  @override
  String get brand => 'Marque';

  @override
  String get serie => 'Série';

  @override
  String get model => 'Modèle';

  @override
  String get serial_n => 'N° de série';

  @override
  String get issue => 'Panne(s)';

  @override
  String get deadline => 'Délai';

  @override
  String get days => 'Jour(s)';

  @override
  String get estimated_price => 'Prix ​​estimé';

  @override
  String get remarks => 'Remarques';

  @override
  String get warranty => 'Garantie';

  @override
  String get emergency => 'Urgence';

  @override
  String get print_receipt => 'Imprimer le reçu';

  @override
  String get cancel_reception => 'Annuler la réception';

  @override
  String get close_after_printing => 'Fermer après l\'impression';

  @override
  String get id => 'ID';

  @override
  String get generate => 'Générateur de codes-barres';

  @override
  String get print_barcode_tooltip => 'Imprimer le code-barres';

  @override
  String get add_client_tooltip => 'Ajouter un nouveau client';

  @override
  String get edit_client_tooltip =>
      'Modifier les informations client sélectionnées';

  @override
  String get select_client_tooltip =>
      'Sélectionnez parmi la liste des clients existants';

  @override
  String get add_device_tooltip => 'Ajouter un nouvel appareil à la liste';

  @override
  String get edit_device_tooltip =>
      'Modifier les informations de l\'appareil sélectionné';

  @override
  String get cancel_device_tooltip => 'Annuler la modification';

  @override
  String get remove_device_tooltip => 'Supprimer l\'appareil de la liste';

  @override
  String get timeline => 'Chronologie';

  @override
  String get all => 'Tout';

  @override
  String get current_year => 'année en cours';

  @override
  String get current_month => 'Mois en cours';

  @override
  String get current_week => 'Semaine en cours';

  @override
  String get timeline_picker => 'Sélecteur de chronologie';

  @override
  String get search => 'Recherche';

  @override
  String get no_filter => 'Sans filtre';

  @override
  String get dealer => 'Revendeur';

  @override
  String get particular => 'Particulier';

  @override
  String get company => 'Entreprise';

  @override
  String get new_reception => 'Nouvelle réception';

  @override
  String get payment => 'Paiement';

  @override
  String get transactions => 'historique des transactions';

  @override
  String get user => 'Utilisateur';

  @override
  String get debts => 'Dettes';

  @override
  String get time => 'Temps';

  @override
  String get date => 'Date';

  @override
  String get from => 'Depuis';

  @override
  String get to => 'À';

  @override
  String get new_reception_tooltip =>
      'Créer une nouvelle réception pour le client sélectionné';

  @override
  String get delete_client_tooltip => 'Supprimer le client sélectionné';

  @override
  String get client_payment_tooltip =>
      'Ajouter un paiement pour le client sélectionné';

  @override
  String get transactions_tooltip =>
      'Gérer l\'historique des transactions d\'un client spécifique';

  @override
  String get phase => 'Phase';

  @override
  String get phase_tooltip => 'Gérer la phase de l\'appareil';

  @override
  String get technician => 'Technicien';

  @override
  String get technician_tooltip =>
      'Technicien qui a travaillé sur cet appareil';

  @override
  String get print_device => 'Imprimer';

  @override
  String get print_device_tooltip =>
      'Imprimer le reçu du propriétaire de l\'appareil';

  @override
  String get situation => 'Situation';

  @override
  String get situation_tooltip => 'Manage device owner situation';

  @override
  String get delivery => 'livraison';

  @override
  String get delivery_tooltip =>
      'Changer le statut de l\'appareil en étape de livraison';

  @override
  String get traceability => 'Traçabilité';

  @override
  String get traceability_tooltip => 'Traçabilité des appareils sélectionnés';

  @override
  String get client => 'Client';

  @override
  String get on_hold => 'En attente';

  @override
  String get diagnostic => 'Diagnostique';

  @override
  String get confirmation => 'Confirmation';

  @override
  String get confirmed => 'Confirmé';

  @override
  String get repaired => 'Réparé';

  @override
  String get not_repaired => 'Non réparé';

  @override
  String get rejected => 'Rejeté';

  @override
  String get parts_unavailable => 'Pièces indisponibles';

  @override
  String get devices_at_workshop => 'Appareils à l\'atelier';

  @override
  String get delivered_devices => 'Appareils livrés';

  @override
  String get cancel_delivery => 'Annuler la livraison';

  @override
  String get cancel_delivery_tooltip => 'Annuler la livraison de l\'appareil';

  @override
  String get edit_owner => 'Modifier le propriétaire';

  @override
  String get edit_owner_tooltip => 'Changer le propriétaire du reçu (client)';

  @override
  String get delete_receipt => 'Supprimer le reçu';

  @override
  String get delete_receipt_tooltip => 'Supprimer définitivement le reçu';

  @override
  String get receipt_n => 'Reçu N°';

  @override
  String get can_not => 'Impossible :/';

  @override
  String get select_device => 'Veuillez d\'abord sélectionner un appareil';

  @override
  String get non => 'non';

  @override
  String get device_managment => 'Gestion des phases de l\'appareil';

  @override
  String get device_price => 'Prix de l\'appareil';

  @override
  String get est_to_final => 'Du prix estimé au prix final';

  @override
  String get price => 'Prix';

  @override
  String get set => 'Définir';

  @override
  String get update => 'Mettre à jour';

  @override
  String get loading => 'Chargement';

  @override
  String get status => 'Statut';

  @override
  String get found => 'trouvé';

  @override
  String get device_phases => 'Phases de l\'appareil';

  @override
  String get change => 'Modifier';

  @override
  String get sms_text => 'SMS';

  @override
  String get auto_sms => 'SMS automatique...';

  @override
  String get send_sms => 'Envoyer un SMS';

  @override
  String get services => 'Services';

  @override
  String get enter => 'Entrer';

  @override
  String get and => 'Et';

  @override
  String get save => 'Enregistrer';

  @override
  String get exit => 'Quitter';

  @override
  String get final_price => 'Prix final';

  @override
  String get priceUpdatedSuccessfully => 'Prix mis à jour avec succès';

  @override
  String get technicianAssignedSuccessfully => 'Technicien assigné avec succès';

  @override
  String phaseChangedSuccessfully(String phase) {
    return 'Phase changée vers \"$phase\" avec succès';
  }

  @override
  String get remarksUpdatedSuccessfully => 'Remarques mises à jour avec succès';

  @override
  String get remarksSavedSuccessfully => 'Remarques sauvegardées avec succès';

  @override
  String get failedToUpdatePrice => 'Échec de la mise à jour du prix';

  @override
  String failedToUpdatePriceWithError(String error) {
    return 'Échec de la mise à jour du prix: $error';
  }

  @override
  String get failedToAssignTechnician =>
      'Échec de l\'assignation du technicien';

  @override
  String failedToAssignTechnicianWithError(String error) {
    return 'Échec de l\'assignation du technicien: $error';
  }

  @override
  String get failedToChangePhase => 'Échec du changement de phase';

  @override
  String failedToChangePhaseWithError(String error) {
    return 'Échec du changement de phase: $error';
  }

  @override
  String get failedToSaveRemarks => 'Échec de la sauvegarde des remarques';

  @override
  String failedToSendSms(String error) {
    return 'Échec de l\'envoi du SMS: $error';
  }

  @override
  String get sendSms => 'Envoyer SMS';

  @override
  String get smsTemplateLoadedMessage =>
      'Un modèle SMS a été chargé pour cette phase. Souhaitez-vous envoyer la notification SMS au client maintenant?';

  @override
  String get smsMessage => 'Message SMS';

  @override
  String get smsError => 'Erreur SMS';

  @override
  String get error => 'Erreur';

  @override
  String technicianWillBe(String technician) {
    return 'Le technicien sera: \"$technician\"';
  }

  @override
  String currentTechnician(String technician) {
    return 'Technicien actuel: $technician';
  }

  @override
  String phaseSuccessfullyChanged(String phase) {
    return 'Phase changée avec succès vers \"$phase\"';
  }

  @override
  String phaseWillChange(String oldPhase, String newPhase) {
    return 'La phase changera de \"$oldPhase\" vers \"$newPhase\"';
  }

  @override
  String get ok => 'OK';

  @override
  String get unknown => 'Inconnu';

  @override
  String get notAssigned => 'Non assigné';

  @override
  String get smsSentSuccessfully => 'SMS envoyé avec succès';

  @override
  String get failedToSendSmsViaFrontline =>
      'Échec de l\'envoi du SMS via Frontline';

  @override
  String smsServiceTimeout(String message) {
    return 'Délai d\'attente du service SMS: $message';
  }

  @override
  String get frontlineSmsNotConfigured => 'Frontline SMS non configuré';

  @override
  String get connectionTimedOut => 'Délai de connexion expiré';

  @override
  String get valid => 'valide';

  @override
  String get deviceUpdatedSuccessfully => 'Appareil mis à jour avec succès';

  @override
  String get failedToUpdateDevice => 'Échec de la mise à jour de l\'appareil';

  @override
  String get confirmDeleteDevice => 'Confirmer la suppression';

  @override
  String get confirmDeleteDeviceMsg =>
      'Êtes-vous sûr de vouloir supprimer cet appareil?';

  @override
  String get deviceDeletedSuccessfully => 'Appareil supprimé avec succès';

  @override
  String get failedToDeleteDevice => 'Échec de la suppression de l\'appareil';

  @override
  String get errorDeletingDevice =>
      'Erreur lors de la suppression de l\'appareil';

  @override
  String get success => 'Succès';
}

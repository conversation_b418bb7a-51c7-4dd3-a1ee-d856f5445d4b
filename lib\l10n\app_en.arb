{"@@locale": "en", "language": "Language", "@language": {}, "dashboard": "Dashboard", "@dashboard": {}, "reception": "Reception", "@reception": {}, "clients": "Clients", "@clients": {}, "devices": "Devices", "@devices": {}, "device": "<PERSON><PERSON>", "@device": {}, "details": "Details", "@details": {}, "receiptList": "Receipt List", "@receiptList": {}, "cashFlow": "Cash Flow", "@cashFlow": {}, "expenses": "Expenses", "@expenses": {}, "products": "Products", "@products": {}, "technicians": "Technicians", "@technicians": {}, "settings": "Settings", "@settings": {}, "exitApp": "Confirm close", "@exitApp": {}, "exitAppMsg": "Are you sure you want to close this window ?", "@exitAppMsg": {}, "yes": "Yes", "@yes": {}, "no": "No", "@no": {}, "new_add": "New", "add": "Add", "edit": "Edit", "cancel": "Cancel", "delete": "Delete", "select": "Select", "name": "Name", "type": "Type", "phone_n": "Phone N°", "device_type": "Device type", "brand": "Brand", "serie": "Serie", "model": "Model", "serial_n": "Serial N°", "issue": "Issue(s)", "deadline": "Deadline", "days": "Day(s)", "estimated_price": "Estimated price", "remarks": "Remarks", "warranty": "Warranty", "emergency": "Emergency", "print_receipt": "Print receipt", "cancel_reception": "Cancel reception", "close_after_printing": "Close after printing", "id": "ID", "generate": "Barcode Generator", "print_barcode_tooltip": "Print Barcode", "add_client_tooltip": "Add new client", "edit_client_tooltip": "Edit selected client information", "select_client_tooltip": "Select from existing clients list", "add_device_tooltip": "Add new device to the list", "edit_device_tooltip": "Edit selected device informations", "cancel_device_tooltip": "Cancel editing", "remove_device_tooltip": "Remove device from the list", "timeline": "Timeline", "all": "All", "current_year": "Current year", "current_month": "Current month", "current_week": "Current week", "timeline_picker": "Timeline picker", "search": "Search", "no_filter": "Without filter", "dealer": "Dealer", "particular": "Particular", "company": "Company", "new_reception": "New reception", "payment": "Payment", "transactions": "transactions history", "user": "User", "debts": "Debts", "time": "Time", "date": "Date", "from": "From", "to": "To", "new_reception_tooltip": "Create new reception for selected client", "delete_client_tooltip": "Delete selected client", "client_payment_tooltip": "Add payment for selected client", "transactions_tooltip": "Manage selected client transactions history", "phase": "Phase", "phase_tooltip": "Manage device phase", "technician": "Technician", "technician_tooltip": "Technician who worked on this device", "print_device": "Print", "print_device_tooltip": "Print device owner receipt", "situation": "Situation", "situation_tooltip": "Manage device owner situation", "delivery": "Delivery", "delivery_tooltip": "Change device status to delivery stage", "traceability": "Traceability", "traceability_tooltip": "Selected device traceability", "client": "Client", "on_hold": "On hold", "diagnostic": "Diagnostic", "confirmation": "Confirmation", "confirmed": "Confirmed", "repaired": "Repaired", "not_repaired": "Not repaired", "rejected": "Rejected", "parts_unavailable": "Part unavailable", "devices_at_workshop": "Devices at workshop", "delivered_devices": "Delivered devices", "cancel_delivery": "Cancel delivery", "cancel_delivery_tooltip": "Cancel device delivery", "edit_owner": "Edit owner", "edit_owner_tooltip": "Change receipt owner (client)", "delete_receipt": "Delete receipt", "delete_receipt_tooltip": "Delete receipt permanently", "receipt_n": "Receipt N°", "can_not": "You can not do that :/", "select_device": "Please select a device first", "non": "non", "device_managment": "Device Phase Management", "device_price": "<PERSON><PERSON>", "est_to_final": "From estimated price to final price", "price": "Price", "set": "Set", "update": "Update", "loading": "Loading", "found": "found", "device_phases": "Device Phases", "change": "Change", "sms_text": "SMS TEXT", "auto_sms": "Auto SMS message...", "send_sms": "Send SMS", "services": "Services", "enter": "Enter", "and": "And", "save": "Save", "exit": "Exit", "final_price": "Final price", "priceUpdatedSuccessfully": "Price updated successfully", "@priceUpdatedSuccessfully": {}, "technicianAssignedSuccessfully": "Technician assigned successfully", "@technicianAssignedSuccessfully": {}, "phaseChangedSuccessfully": "Phase changed to \"{phase}\" successfully", "@phaseChangedSuccessfully": {"placeholders": {"phase": {"type": "String"}}}, "remarksUpdatedSuccessfully": "Remarks updated successfully", "@remarksUpdatedSuccessfully": {}, "remarksSavedSuccessfully": "Remarks saved successfully", "@remarksSavedSuccessfully": {}, "failedToUpdatePrice": "Failed to update price", "@failedToUpdatePrice": {}, "failedToUpdatePriceWithError": "Failed to update price: {error}", "@failedToUpdatePriceWithError": {"placeholders": {"error": {"type": "String"}}}, "failedToAssignTechnician": "Failed to assign technician", "@failedToAssignTechnician": {}, "failedToAssignTechnicianWithError": "Failed to assign technician: {error}", "@failedToAssignTechnicianWithError": {"placeholders": {"error": {"type": "String"}}}, "failedToChangePhase": "Failed to change phase", "@failedToChangePhase": {}, "failedToChangePhaseWithError": "Failed to change phase: {error}", "@failedToChangePhaseWithError": {"placeholders": {"error": {"type": "String"}}}, "failedToSaveRemarks": "Failed to save remarks", "@failedToSaveRemarks": {}, "failedToSendSms": "Failed to send SMS: {error}", "@failedToSendSms": {"placeholders": {"error": {"type": "String"}}}, "sendSms": "Send SMS", "@sendSms": {}, "smsTemplateLoadedMessage": "An SMS template has been loaded for this phase. Would you like to send the SMS notification to the client now?", "@smsTemplateLoadedMessage": {}, "smsMessage": "SMS Message", "@smsMessage": {}, "smsError": "SMS Error", "@smsError": {}, "error": "Error", "@error": {}, "technicianWillBe": "Technician will be: \"{technician}\"", "@technicianWillBe": {"placeholders": {"technician": {"type": "String"}}}, "currentTechnician": "Current technician: {technician}", "@currentTechnician": {"placeholders": {"technician": {"type": "String"}}}, "phaseSuccessfullyChanged": "Phase successfully changed to \"{phase}\"", "@phaseSuccessfullyChanged": {"placeholders": {"phase": {"type": "String"}}}, "phaseWillChange": "Phase will change from \"{oldPhase}\" to \"{newPhase}\"", "@phaseWillChange": {"placeholders": {"oldPhase": {"type": "String"}, "newPhase": {"type": "String"}}}, "ok": "OK", "@ok": {}, "unknown": "Unknown", "@unknown": {}, "notAssigned": "Not assigned", "@notAssigned": {}, "smsSentSuccessfully": "SMS sent successfully", "@smsSentSuccessfully": {}, "failedToSendSmsViaFrontline": "Failed to send SMS via Frontline", "@failedToSendSmsViaFrontline": {}, "smsServiceTimeout": "SMS service timeout: {message}", "@smsServiceTimeout": {"placeholders": {"message": {"type": "String"}}}, "frontlineSmsNotConfigured": "Frontline SMS not configured", "@frontlineSmsNotConfigured": {}, "connectionTimedOut": "Connection timed out", "@connectionTimedOut": {}, "valid": "valid", "@valid": {}, "deviceUpdatedSuccessfully": "Device updated successfully", "@deviceUpdatedSuccessfully": {}, "failedToUpdateDevice": "Failed to update device", "@failedToUpdateDevice": {}, "confirmDeleteDevice": "Confirm Delete", "@confirmDeleteDevice": {}, "confirmDeleteDeviceMsg": "Are you sure you want to remove this device?", "@confirmDeleteDeviceMsg": {}, "deviceDeletedSuccessfully": "<PERSON>ce deleted successfully", "@deviceDeletedSuccessfully": {}, "failedToDeleteDevice": "Failed to delete device", "@failedToDeleteDevice": {}, "errorDeletingDevice": "Error deleting device", "@errorDeletingDevice": {}, "success": "Success", "@success": {}}
import 'package:carbon_icons/carbon_icons.dart';
import 'package:fluent_ui/fluent_ui.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:workshop_studio/l10n/gen_l10n/app_localizations.dart';
import 'package:workshop_studio/models/device/device_model.dart';
import 'package:workshop_studio/providers/devices/at_workshop/at_workshop_notifier.dart';

void showDeviceDetailsDialog(BuildContext context, DeviceModel device, WidgetRef ref) {
  showDialog(
    context: context,
    builder: (context) => DeviceDetailsDialog(device: device, ref: ref),
  );
}

class DeviceDetailsDialog extends StatefulWidget {
  final DeviceModel device;
  final WidgetRef ref;

  const DeviceDetailsDialog({super.key, required this.device, required this.ref});

  @override
  State<DeviceDetailsDialog> createState() => _DeviceDetailsDialogState();
}

class _DeviceDetailsDialogState extends State<DeviceDetailsDialog> {
  String? technician;
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadTechnicianInfo();
  }

  Future<void> _loadTechnicianInfo() async {
    try {
      final deviceNotifier = widget.ref.read(deviceProvider("devicesAtWorkshop").notifier);
      final technicianName = await deviceNotifier.getTechnicianForDevice(
        widget.device.machineId,
      );
      
      if (mounted) {
        setState(() {
          technician = technicianName;
          isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          technician = null;
          isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final lang = AppLocalizations.of(context);

    return ContentDialog(
      constraints: BoxConstraints(
        maxWidth: 600,
        maxHeight: 700,
        minWidth: 500,
        minHeight: 400,
      ),
      title: Row(
        children: [
          Icon(CarbonIcons.information, size: 20),
          SizedBox(width: 8),
          Text('${lang.device} Information'),
        ],
      ),
      content: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          spacing: 16,
          children: [
            // Client Information Section
            _buildDetailsSection(lang.client, [
              _buildDetailRow('${lang.name}:', widget.device.clientName ?? 'N/A'),
              _buildDetailRow(
                '${lang.phone_n}:',
                widget.device.clientPhone ?? 'N/A',
              ),
            ]),

            // Device Information Section
            _buildDetailsSection(lang.device, [
              _buildDetailRow('${lang.device_type}:', widget.device.machineType),
              _buildDetailRow('${lang.brand}:', widget.device.machineBrand),
              _buildDetailRow(
                '${lang.serie}:',
                widget.device.machineSerie ?? 'N/A',
              ),
              _buildDetailRow(
                '${lang.model}:',
                widget.device.machineModel ?? 'N/A',
              ),
              _buildDetailRow(
                '${lang.serial_n}:',
                widget.device.serialNumber ?? 'N/A',
              ),
            ]),

            // Issue Information Section
            _buildDetailsSection(lang.issue, [
              _buildDetailRow('${lang.issue}:', widget.device.problem ?? 'N/A'),
              _buildDetailRow(
                '${lang.estimated_price}:',
                '${widget.device.estimatedPrice.toStringAsFixed(2)} DA',
              ),
              _buildDetailRow(
                '${lang.deadline}:',
                '${widget.device.deadline} ${lang.days}',
              ),
            ]),

            // Status Information Section
            _buildDetailsSection('Status', [
              _buildDetailRow(
                '${lang.warranty}:',
                widget.device.warranty == 1 ? lang.yes : lang.no,
              ),
              _buildDetailRow(
                '${lang.emergency}:',
                widget.device.emergency == 1 ? lang.yes : lang.no,
              ),
              _buildDetailRow(
                '${lang.technician}:',
                isLoading ? 'Loading...' : (technician ?? lang.notAssigned),
              ),
            ]),

            // Additional Information Section
            if (widget.device.note != null && widget.device.note!.isNotEmpty)
              _buildDetailsSection(lang.remarks, [
                Container(
                  width: double.infinity,
                  padding: EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: Colors.grey.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Text(widget.device.note!, style: TextStyle(fontSize: 14)),
                ),
              ]),
          ],
        ),
      ),
      actions: [
        Button(
          child: Text(lang.cancel),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ],
    );
  }

  /// Build a details section with title and content
  Widget _buildDetailsSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.blue.lightest,
          ),
        ),
        SizedBox(height: 8),
        Container(
          width: double.infinity,
          padding: EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey.withValues(alpha: 0.05),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey.withValues(alpha: 0.2)),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            spacing: 8,
            children: children,
          ),
        ),
      ],
    );
  }

  /// Build a detail row with label and value
  Widget _buildDetailRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 120,
          child: Text(
            label,
            style: TextStyle(
              fontWeight: FontWeight.w500,
              color: Colors.grey[100],
            ),
          ),
        ),
        Expanded(
          child: Text(
            value.isNotEmpty ? value : 'N/A',
            style: TextStyle(color: Colors.grey[10]),
          ),
        ),
      ],
    );
  }
}

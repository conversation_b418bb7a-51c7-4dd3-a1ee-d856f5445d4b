import 'package:carbon_icons/carbon_icons.dart';
import 'package:fluent_ui/fluent_ui.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:workshop_studio/presentation/pages/devices/widgets/data_grid/devices_atworkshop_datagrid.dart';
import 'package:workshop_studio/presentation/pages/devices/widgets/dialogs/device_phase_dialog.dart';
import 'package:workshop_studio/presentation/pages/devices/widgets/dialogs/device_edit_dialog.dart';
import 'package:workshop_studio/presentation/widgets/card_highlight.dart';
import 'package:workshop_studio/l10n/gen_l10n/app_localizations.dart';
import 'package:workshop_studio/providers/clients/search/search_query_notifier.dart';
import 'package:workshop_studio/providers/devices/at_workshop/at_workshop_notifier.dart';
import 'package:workshop_studio/providers/devices/at_workshop/at_workshop_crud_provider.dart';
import 'package:workshop_studio/models/device/device_model.dart';

class DevicesAtWorkshop extends ConsumerStatefulWidget {
  const DevicesAtWorkshop({super.key});

  @override
  ConsumerState<DevicesAtWorkshop> createState() => _DevicesAtWorkshopState();
}

class _DevicesAtWorkshopState extends ConsumerState<DevicesAtWorkshop> {
  TextEditingController searchController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    final lang = AppLocalizations.of(context);
    final search = ref.watch(searchQueryProvider('devicesAtWorkshop'));
    final searchNotifier = ref.read(
      searchQueryProvider('devicesAtWorkshop').notifier,
    );
    final phaseFilter = ref.watch(phaseFilterProvider('devicesAtWorkshop'));
    final phaseFilterNotifier = ref.read(
      phaseFilterProvider('devicesAtWorkshop').notifier,
    );

    // Keep TextBox in sync with provider
    if (searchController.text != search) {
      searchController.text = search;
      searchController.selection = TextSelection.fromPosition(
        TextPosition(offset: search.length),
      );
    }

    return Column(
      spacing: 8,
      children: [
        CardHighlight(
          child: Row(
            spacing: 18,
            children: [
              Expanded(
                flex: 1,
                child: TextBox(
                  controller: searchController,
                  expands: false,
                  suffix: IconButton(
                    icon: const Icon(FluentIcons.clear),
                    onPressed: () {
                      searchNotifier.clear();
                      ref
                          .read(deviceProvider("devicesAtWorkshop").notifier)
                          .reset();
                    },
                  ),
                  placeholder: lang.search,
                  onChanged: (value) => searchNotifier.setQuery(value),
                  prefix: Padding(
                    padding: EdgeInsets.all(8),
                    child: Icon(CarbonIcons.search),
                  ),
                ),
              ),
              ComboBox<String>(
                value: phaseFilter?.toString() ?? "all",
                items: [
                  ComboBoxItem(value: "all", child: Text(lang.no_filter)),
                  ComboBoxItem(value: '0', child: Text(lang.on_hold)),
                  ComboBoxItem(value: '1', child: Text(lang.diagnostic)),
                  ComboBoxItem(value: '2', child: Text(lang.confirmation)),
                  ComboBoxItem(value: '3', child: Text(lang.confirmed)),
                  ComboBoxItem(value: '4', child: Text(lang.repaired)),
                  ComboBoxItem(value: '5', child: Text(lang.not_repaired)),
                  ComboBoxItem(value: '6', child: Text(lang.parts_unavailable)),
                  ComboBoxItem(value: '7', child: Text(lang.rejected)),
                ],
                onChanged: (value) {
                  if (value == "all") {
                    phaseFilterNotifier.state = null;
                  } else {
                    phaseFilterNotifier.state = int.tryParse(value!);
                  }
                  // Reset to first page and reload devices
                  ref
                      .read(deviceProvider("devicesAtWorkshop").notifier)
                      .reset();
                },
              ),
              Spacer(flex: 2),
              IconButton(
                icon: Icon(FluentIcons.refresh, size: 18.0),
                onPressed: () {
                  ref
                      .read(deviceProvider("devicesAtWorkshop").notifier)
                      .reset();
                },
              ),
            ],
          ),
        ),
        CommandBar(
          overflowBehavior: CommandBarOverflowBehavior.wrap,
          primaryItems: [
            CommandBarButton(
              icon: const Icon(CarbonIcons.queued, size: 22),
              label: Text(lang.phase),
              tooltip: lang.phase_tooltip,
              onPressed: () async {
                final selectedDevice = ref.read(selectedDeviceProvider);
                if (selectedDevice == null) {
                  await unselectedDeviceInfoBar(context, lang);
                  return;
                }
                showDevicePhaseDialog(context, ref);
              },
            ),
            CommandBarButton(
              icon: const Icon(FluentIcons.edit),
              label: Text(lang.edit),
              tooltip: lang.edit_device_tooltip,
              onPressed: () async {
                final selectedDevice = ref.read(selectedDeviceProvider);
                if (selectedDevice == null) {
                  await unselectedDeviceInfoBar(context, lang);
                  return;
                }
                showDeviceEditDialog(context, ref);
              },
            ),
            CommandBarButton(
              icon: const Icon(FluentIcons.delete),
              label: Text(lang.delete),
              tooltip: lang.remove_device_tooltip,
              onPressed: () async {
                final selectedDevice = ref.read(selectedDeviceProvider);
                if (selectedDevice == null) {
                  await unselectedDeviceInfoBar(context, lang);
                  return;
                }

                // Show confirmation dialog following the Python pattern
                final result = await showDialog<bool>(
                  context: context,
                  builder:
                      (context) => ContentDialog(
                        title: Text(lang.confirmDeleteDevice),
                        content: Text(lang.confirmDeleteDeviceMsg),
                        actions: [
                          Button(
                            child: Text(lang.no),
                            onPressed: () => Navigator.of(context).pop(false),
                          ),
                          FilledButton(
                            child: Text(lang.yes),
                            onPressed: () => Navigator.of(context).pop(true),
                          ),
                        ],
                      ),
                );

                if (result == true) {
                  // Get the CRUD provider and delete the device
                  final deviceCrud = ref.read(
                    deviceCrudProvider('devicesAtWorkshop'),
                  );
                  final deleteResult = await deviceCrud.deleteDevice(
                    selectedDevice.machineId,
                  );

                  if (deleteResult['success']) {
                    // Show success message
                    if (context.mounted) {
                      displayInfoBar(
                        context,
                        builder:
                            (context, close) => InfoBar(
                              title: Text(lang.success),
                              content: Text(deleteResult['message']),
                              severity: InfoBarSeverity.success,
                              action: IconButton(
                                icon: const Icon(FluentIcons.clear),
                                onPressed: close,
                              ),
                            ),
                      );
                    }

                    // Remove only the specific device from the list (no full refresh)
                    ref
                        .read(deviceProvider("devicesAtWorkshop").notifier)
                        .removeDeviceFromList(selectedDevice.machineId);
                  } else {
                    // Show error message
                    if (context.mounted) {
                      displayInfoBar(
                        context,
                        builder:
                            (context, close) => InfoBar(
                              title: Text(lang.error),
                              content: Text(deleteResult['message']),
                              severity: InfoBarSeverity.error,
                              action: IconButton(
                                icon: const Icon(FluentIcons.clear),
                                onPressed: close,
                              ),
                            ),
                      );
                    }
                  }
                }
              },
            ),
            CommandBarButton(
              icon: const Icon(CarbonIcons.information, size: 22),
              label: Text('Details'),
              tooltip: 'Device details',
              onPressed: () async {
                final selectedDevice = ref.read(selectedDeviceProvider);
                if (selectedDevice == null) {
                  await unselectedDeviceInfoBar(context, lang);
                  return;
                }

                // Show device details dialog
                await showDeviceDetailsDialog(context, selectedDevice, ref);
              },
            ),
            CommandBarSeparator(color: Colors.grey[50]),
            CommandBarButton(
              icon: const Icon(CarbonIcons.delivery, size: 22),
              label: Text(lang.delivery),
              tooltip: lang.delivery_tooltip,
              onPressed: () {},
            ),
            CommandBarButton(
              icon: const Icon(FluentIcons.full_history),
              label: Text(lang.traceability),
              tooltip: lang.traceability_tooltip,
              onPressed: () {},
            ),
            CommandBarButton(
              icon: const Icon(FluentIcons.payment_card),
              label: Text(lang.situation),
              tooltip: lang.situation_tooltip,
              onPressed: () {},
            ),
            CommandBarSeparator(color: Colors.grey[50]),
            CommandBarButton(
              icon: const Icon(FluentIcons.user_event),
              label: Text(lang.technician),
              tooltip: lang.technician_tooltip,
              onPressed: () {},
            ),
            CommandBarSeparator(color: Colors.grey[50]),
            CommandBarButton(
              icon: const Icon(FluentIcons.print),
              label: Text(lang.print_device),
              tooltip: lang.print_device_tooltip,
              onPressed: () {},
            ),
          ],
        ),
        // Replace Flexible with Expanded to avoid ParentDataWidget conflict
        Expanded(flex: 1, child: DevicesAtworkshopDataGrid()),
      ],
    );
  }
}

/// Show device details dialog with all device information
Future<void> showDeviceDetailsDialog(
  BuildContext context,
  DeviceModel device,
  WidgetRef ref,
) async {
  final lang = AppLocalizations.of(context);

  // Get technician information
  final deviceNotifier = ref.read(deviceProvider("devicesAtWorkshop").notifier);
  final technician = await deviceNotifier.getTechnicianForDevice(
    device.machineId,
  );

  // Check if the widget is still mounted after the async operation
  if (!context.mounted) return;

  await showDialog(
    context: context,
    builder:
        (context) => ContentDialog(
          constraints: BoxConstraints(
            maxWidth: 600,
            maxHeight: 700,
            minWidth: 500,
            minHeight: 400,
          ),
          title: Row(
            children: [
              Icon(CarbonIcons.information, size: 20),
              SizedBox(width: 8),
              Text('${lang.device} Information'),
            ],
          ),
          content: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              spacing: 16,
              children: [
                // Client Information Section
                _buildDetailsSection(lang.client, [
                  _buildDetailRow('${lang.name}:', device.clientName ?? 'N/A'),
                  _buildDetailRow(
                    '${lang.phone_n}:',
                    device.clientPhone ?? 'N/A',
                  ),
                ]),

                // Device Information Section
                _buildDetailsSection(lang.device, [
                  _buildDetailRow('${lang.device_type}:', device.machineType),
                  _buildDetailRow('${lang.brand}:', device.machineBrand),
                  _buildDetailRow(
                    '${lang.serie}:',
                    device.machineSerie ?? 'N/A',
                  ),
                  _buildDetailRow(
                    '${lang.model}:',
                    device.machineModel ?? 'N/A',
                  ),
                  _buildDetailRow(
                    '${lang.serial_n}:',
                    device.serialNumber ?? 'N/A',
                  ),
                ]),

                // Issue Information Section
                _buildDetailsSection(lang.issue, [
                  _buildDetailRow('${lang.issue}:', device.problem ?? 'N/A'),
                  _buildDetailRow(
                    '${lang.estimated_price}:',
                    '${device.estimatedPrice.toStringAsFixed(2)} DA',
                  ),
                  _buildDetailRow(
                    '${lang.deadline}:',
                    '${device.deadline} ${lang.days}',
                  ),
                ]),

                // Status Information Section
                _buildDetailsSection('Status', [
                  _buildDetailRow(
                    '${lang.warranty}:',
                    device.warranty == 1 ? lang.yes : lang.no,
                  ),
                  _buildDetailRow(
                    '${lang.emergency}:',
                    device.emergency == 1 ? lang.yes : lang.no,
                  ),
                  _buildDetailRow(
                    '${lang.technician}:',
                    technician ?? lang.notAssigned,
                  ),
                ]),

                // Additional Information Section
                if (device.note != null && device.note!.isNotEmpty)
                  _buildDetailsSection(lang.remarks, [
                    Container(
                      width: double.infinity,
                      padding: EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.grey.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: Colors.grey.withValues(alpha: 0.3),
                        ),
                      ),
                      child: Text(device.note!, style: TextStyle(fontSize: 14)),
                    ),
                  ]),
              ],
            ),
          ),
          actions: [
            Button(
              child: Text(lang.cancel),
              onPressed: () => Navigator.of(context).pop(),
            ),
          ],
        ),
  );
}

/// Build a details section with title and content
Widget _buildDetailsSection(String title, List<Widget> children) {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Text(
        title,
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
          color: Colors.blue.lightest,
        ),
      ),
      SizedBox(height: 8),
      Container(
        width: double.infinity,
        padding: EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.grey.withValues(alpha: 0.05),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey.withValues(alpha: 0.2)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          spacing: 8,
          children: children,
        ),
      ),
    ],
  );
}

/// Build a detail row with label and value
Widget _buildDetailRow(String label, String value) {
  return Row(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      SizedBox(
        width: 120,
        child: Text(
          label,
          style: TextStyle(
            fontWeight: FontWeight.w500,
            color: Colors.grey[100],
          ),
        ),
      ),
      Expanded(
        child: Text(
          value.isNotEmpty ? value : 'N/A',
          style: TextStyle(color: Colors.grey[10]),
        ),
      ),
    ],
  );
}

Future<void> unselectedDeviceInfoBar(
  BuildContext context,
  AppLocalizations lang,
) {
  return displayInfoBar(
    context,
    builder: (context, close) {
      return InfoBar(
        title: Text(lang.can_not),
        content: Text(lang.select_device),
        action: IconButton(
          icon: const Icon(FluentIcons.clear),
          onPressed: close,
        ),
        severity: InfoBarSeverity.warning,
      );
    },
  );
}
